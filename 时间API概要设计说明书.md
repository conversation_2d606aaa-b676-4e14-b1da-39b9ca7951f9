# TEE时间API概要设计说明书

## 1 引言

### 1.1 编写目的
本文档旨在详细描述TEE（Trusted Execution Environment）时间API的概要设计，为开发人员提供时间管理功能的技术实现指导。读者对象包括：
- TEE系统架构师
- TEE内核开发人员
- TA（Trusted Application）开发人员
- 系统集成工程师

### 1.2 背景
- **软件产品名称**：TEE时间管理子系统
- **产品代码**：TEE-TIME-API
- **任务提出者**：GlobalPlatform标准组织
- **开发者**：TEE系统开发团队
- **用户**：TA开发者和TEE应用
- **运行环境**：支持TEE的ARM TrustZone或类似安全硬件平台

### 1.3 术语与缩写解释

| 缩写、术语 | 解释 |
|-----------|------|
| TEE | Trusted Execution Environment，可信执行环境 |
| TA | Trusted Application，可信应用 |
| REE | Rich Execution Environment，富执行环境 |
| RTC | Real Time Clock，实时时钟 |
| UTC | Coordinated Universal Time，协调世界时 |
| GP | GlobalPlatform，全球平台标准 |

### 1.4 参考资料
1. GlobalPlatform TEE Internal Core API Specification v1.3
2. ARM TrustZone技术参考手册
3. TEE系统需求规格说明书
4. 可信存储设计规范

## 2 概述

### 2.1 系统任务

#### 2.1.1 系统目标
实现符合GlobalPlatform标准的TEE时间管理API，提供：
- 系统时间获取功能
- TA持久化时间管理
- REE时间查询接口
- 时间等待机制

#### 2.1.2 运行环境
- **硬件要求**：支持TrustZone的ARM处理器，具备安全定时器或RTC
- **软件要求**：TEE内核、可信存储服务、安全监控器

#### 2.1.3 设计策略
- **扩展策略**：模块化设计，支持不同硬件定时器源的适配
- **复用策略**：时间基础结构可被其他TEE服务复用
- **折衷策略**：在安全性和性能之间平衡，支持不同保护级别

#### 2.1.4 与其它系统关系
- 依赖TEE内核的任务调度机制
- 与可信存储系统集成实现持久化
- 与REE系统时间服务交互

### 2.2 需求规定

#### 2.2.1 功能需求
1. **系统时间管理**：提供单调递增的系统时间
2. **TA持久化时间**：支持TA级别的持久时间设置和查询
3. **REE时间查询**：获取REE系统时间
4. **时间等待功能**：支持可取消的时间等待

#### 2.2.2 非功能性需求
- **精度要求**：系统时间偏差每天不超过±10秒
- **单调性**：系统时间必须单调递增，不受低功耗影响
- **原子性**：TA持久时间设置必须原子化
- **持久性**：TA持久时间跨设备重启保持

#### 2.2.3 约束和假定
- 系统必须支持两种保护级别（100和1000）
- 依赖硬件RTC或安全定时器
- 受可信存储容量限制

## 3 总体设计

### 3.1 软件系统结构

```
TEE时间API系统
├── 时间API接口层
│   ├── TEE_GetSystemTime
│   ├── TEE_Wait
│   ├── TEE_GetTAPersistentTime
│   ├── TEE_SetTAPersistentTime
│   └── TEE_GetREETime
├── 时间管理核心层
│   ├── 系统时间管理模块
│   ├── TA持久时间管理模块
│   └── REE时间接口模块
├── 硬件抽象层
│   ├── 安全定时器驱动
│   └── RTC驱动接口
└── 存储接口层
    └── 可信存储接口
```

#### 3.1.1 时间API接口层
提供符合GP标准的时间API接口，处理参数验证和错误码转换。

#### 3.1.2 时间管理核心层
- **系统时间管理模块**：维护单调递增的系统时间，支持不同保护级别
- **TA持久时间管理模块**：管理TA级别的持久时间状态机
- **REE时间接口模块**：与REE时间服务交互

#### 3.1.3 硬件抽象层
封装不同硬件定时器的访问接口，支持安全定时器和普通RTC。

#### 3.1.4 存储接口层
提供TA持久时间的可信存储访问接口。

### 3.2 功能需求与系统模块的关系

| | 时间API接口层 | 时间管理核心层 | 硬件抽象层 | 存储接口层 |
|---|---|---|---|---|
| 系统时间获取 | √ | √ | √ | |
| 时间等待功能 | √ | √ | √ | |
| TA持久时间管理 | √ | √ | | √ |
| REE时间查询 | √ | √ | | |

### 3.3 制作购买或复用的分析

| 模块名称 | 模块实现类型 |
|---------|-------------|
| 时间API接口层 | 自主开发 |
| 系统时间管理模块 | 自主开发 |
| TA持久时间管理模块 | 自主开发 |
| REE时间接口模块 | 自主开发 |
| 安全定时器驱动 | 复用（硬件厂商提供） |
| RTC驱动接口 | 复用（系统现有） |
| 可信存储接口 | 复用（TEE系统现有） |

### 3.4 业务流程

#### 3.4.1 系统时间获取流程
**流程描述**：TA调用TEE_GetSystemTime获取当前系统时间
1. TA调用TEE_GetSystemTime API
2. 验证参数有效性
3. 根据保护级别选择时间源
4. 读取硬件定时器值
5. 转换为TEE_Time格式
6. 返回时间值给TA

#### 3.4.2 TA持久时间设置流程
**流程描述**：TA设置自己的持久时间基准点
1. TA调用TEE_SetTAPersistentTime
2. 验证参数和权限
3. 原子性写入可信存储
4. 更新TA时间状态为SUCCESS
5. 返回操作结果

#### 3.4.3 TA持久时间查询流程
**流程描述**：TA查询自己的持久时间
1. TA调用TEE_GetTAPersistentTime
2. 检查TA时间状态
3. 如果未设置，返回TIME_NOT_SET
4. 如果需要重置，返回TIME_NEEDS_RESET
5. 计算当前持久时间
6. 检查溢出情况
7. 返回时间值和状态

### 3.5 接口设计

#### 3.5.1 用户接口
```c
// 时间结构体定义
typedef struct {
    uint32_t seconds;  // 秒数
    uint32_t millis;   // 毫秒数
} TEE_Time;

// API函数声明
void TEE_GetSystemTime(TEE_Time* time);
TEE_Result TEE_Wait(uint32_t timeout);
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time);
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time);
void TEE_GetREETime(TEE_Time* time);
```

#### 3.5.2 外部接口
- **硬件定时器接口**：访问安全定时器和RTC硬件
- **REE时间服务接口**：通过安全监控器调用REE时间服务
- **可信存储接口**：读写TA持久时间数据

#### 3.5.3 内部接口
- **时间源抽象接口**：统一不同硬件定时器的访问方式
- **状态管理接口**：管理TA持久时间状态机
- **原子操作接口**：保证持久时间设置的原子性

### 3.6 数据结构设计

#### 3.6.1 逻辑结构设计要点
```c
// TA持久时间状态
typedef enum {
    TA_TIME_NOT_SET,
    TA_TIME_SUCCESS,
    TA_TIME_NEEDS_RESET
} ta_time_state_t;

// TA时间管理结构
typedef struct {
    TEE_Time base_time;        // 基准时间
    ta_time_state_t state;     // 时间状态
    uint32_t protection_level; // 保护级别
} ta_time_context_t;
```

#### 3.6.2 物理结构设计要点
- **可信存储布局**：TA持久时间数据存储在专用的可信存储区域
- **内存对齐**：时间结构体按照32位边界对齐
- **原子性保证**：使用事务机制确保写入原子性

## 4 系统出错处理设计

### 4.1 出错信息
| 错误类型 | 返回码 | 处理方法 |
|---------|--------|----------|
| 时间未设置 | TEE_ERROR_TIME_NOT_SET | 提示TA调用设置函数 |
| 时间需要重置 | TEE_ERROR_TIME_NEEDS_RESET | 建议重新同步时间 |
| 时间溢出 | TEE_ERROR_OVERFLOW | 截断秒数，返回溢出标志 |
| 内存不足 | TEE_ERROR_OUT_OF_MEMORY | 释放资源后重试 |
| 存储空间不足 | TEE_ERROR_STORAGE_NO_SPACE | 清理存储空间 |

### 4.2 补救措施
- **后备技术**：当安全定时器不可用时，降级使用REE定时器
- **降效技术**：保护级别从1000降级到100
- **恢复技术**：检测到时间异常时自动触发重置流程

## 5 系统维护设计
- 提供时间状态查询接口用于系统诊断
- 实现时间源切换的热插拔支持
- 设计时间精度监控和告警机制

## 6 尚未解决的问题
1. 多核环境下时间同步机制的具体实现
2. 时间回滚检测算法的优化
3. 大规模TA部署时的存储空间管理策略