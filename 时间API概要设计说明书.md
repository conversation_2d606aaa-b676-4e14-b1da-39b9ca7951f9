# TEE时间API概要设计说明书

## 1 引言

### 1.1 编写目的
本文档旨在详细描述TEE（Trusted Execution Environment）时间API的概要设计，为开发人员提供时间管理功能的技术实现指导。读者对象包括：
- TEE系统架构师
- TEE内核开发人员
- TA（Trusted Application）开发人员
- 系统集成工程师

### 1.2 背景
- **软件产品名称**：TEE时间管理子系统
- **产品代码**：TEE-TIME-API
- **任务提出者**：GlobalPlatform标准组织
- **开发者**：TEE系统开发团队
- **用户**：TA开发者和TEE应用
- **运行环境**：支持TEE的ARM TrustZone或类似安全硬件平台

### 1.3 术语与缩写解释

| 缩写、术语 | 解释 |
|-----------|------|
| TEE | Trusted Execution Environment，可信执行环境 |
| TA | Trusted Application，可信应用 |
| REE | Rich Execution Environment，富执行环境 |
| RTC | Real Time Clock，实时时钟 |
| UTC | Coordinated Universal Time，协调世界时 |
| GP | GlobalPlatform，全球平台标准 |

### 1.4 参考资料
1. GlobalPlatform TEE Internal Core API Specification v1.3
2. ARM TrustZone技术参考手册
3. TEE系统需求规格说明书
4. 可信存储设计规范

## 2 概述

### 2.1 系统任务

#### 2.1.1 系统目标
实现符合GlobalPlatform标准的TEE时间管理API，提供：
- 系统时间获取功能
- TA持久化时间管理
- REE时间查询接口
- 时间等待机制

#### 2.1.2 运行环境
- **硬件要求**：支持TrustZone的ARM处理器，具备安全定时器或RTC
- **软件要求**：TEE内核、可信存储服务、安全监控器

#### 2.1.3 设计策略
- **扩展策略**：模块化设计，支持不同硬件定时器源的适配
- **复用策略**：时间基础结构可被其他TEE服务复用
- **折衷策略**：在安全性和性能之间平衡，支持不同保护级别

#### 2.1.4 与其它系统关系
- 依赖TEE内核的任务调度机制
- 与可信存储系统集成实现持久化
- 与REE系统时间服务交互

### 2.2 需求规定

#### 2.2.1 功能需求
1. **系统时间管理**：提供单调递增的系统时间
2. **TA持久化时间**：支持TA级别的持久时间设置和查询
3. **REE时间查询**：获取REE系统时间
4. **时间等待功能**：支持可取消的时间等待

#### 2.2.2 非功能性需求
- **精度要求**：系统时间偏差每天不超过±10秒
- **单调性**：系统时间必须单调递增，不受低功耗影响
- **原子性**：TA持久时间设置必须原子化
- **持久性**：TA持久时间跨设备重启保持

#### 2.2.3 约束和假定
- 系统必须支持两种保护级别（100和1000）
- 依赖硬件RTC或安全定时器
- 受可信存储容量限制

## 3 总体设计

### 3.1 软件系统结构

```
TEE时间API系统
├── GlobalPlatform API层 (libutee)
│   ├── TEE_GetSystemTime
│   ├── TEE_Wait
│   ├── TEE_GetTAPersistentTime
│   ├── TEE_SetTAPersistentTime
│   └── TEE_GetREETime
├── RCTEE时间服务层 (libc-rctee)
│   ├── rctee_gettime
│   ├── rctee_nanosleep
│   ├── clock_gettime
│   └── nanosleep
├── 系统调用层 (rctee_core)
│   ├── sys_gettime
│   ├── sys_nanosleep
│   └── sys_wait
├── 内核时间管理层 (lk)
│   ├── current_time_ns
│   ├── thread_sleep_ns
│   └── timer管理
├── 硬件抽象层
│   ├── ARM Generic Timer (arm_generic_timer.c)
│   ├── SysTick Timer (systick.c)
│   └── 单调时间源 (monotonic_time_s)
└── 存储服务层
    ├── 可信存储服务 (storage)
    └── TA属性管理 (property service)
```

#### 3.1.1 GlobalPlatform API层 (libutee)
提供符合GP标准的TEE时间API接口，实现在`user/base/lib/libutee`目录中：
- 处理TEE_Time结构体和错误码转换
- 实现GP标准规定的时间API函数签名
- 与下层RCTEE时间服务进行交互

#### 3.1.2 RCTEE时间服务层 (libc-rctee)
位于`user/base/lib/libc-rctee/time.c`，提供RCTEE特定的时间服务：
- **rctee_gettime**：获取指定时钟源的时间（纳秒精度）
- **rctee_nanosleep**：纳秒级睡眠功能
- **clock_gettime/nanosleep**：POSIX兼容接口
- 支持多种时钟类型（CLOCK_REALTIME, CLOCK_MONOTONIC等）

#### 3.1.3 系统调用层 (rctee_core)
位于`kernel/rctee/lib/rctee/rctee_core/syscall.c`：
- **sys_gettime**：系统调用实现，支持单调时间和通用时间
- **sys_nanosleep**：睡眠系统调用，基于thread_sleep_ns
- **sys_wait**：事件等待系统调用，支持超时机制

#### 3.1.4 内核时间管理层 (lk)
LittleKernel时间管理子系统：
- **current_time_ns**：获取当前纳秒时间戳
- **thread_sleep_ns**：线程纳秒级睡眠
- **timer管理**：定时器设置和回调机制

#### 3.1.5 硬件抽象层
支持多种硬件定时器：
- **ARM Generic Timer**：ARMv7/v8架构的通用定时器
- **SysTick Timer**：ARM Cortex-M系列的系统定时器
- **单调时间源**：IMX平台的单调时间实现

#### 3.1.6 存储服务层
- **可信存储服务**：用于TA持久时间数据的原子性存储
- **TA属性管理**：通过property service管理TA时间相关属性

### 3.2 功能需求与系统模块的关系

| | GP API层 | RCTEE服务层 | 系统调用层 | 内核层 | 硬件层 | 存储层 |
|---|---|---|---|---|---|---|
| 系统时间获取 | √ | √ | √ | √ | √ | |
| 时间等待功能 | √ | √ | √ | √ | √ | |
| TA持久时间管理 | √ | | | | | √ |
| REE时间查询 | √ | √ | √ | √ | | |
| 时间保护级别查询 | √ | √ | √ | | √ | |
| 时间属性查询(直接) | √ | √ | √ | | | |

### 3.3 制作购买或复用的分析

| 模块名称 | 模块实现类型 | 代码位置 |
|---------|-------------|----------|
| GlobalPlatform API层 | 自主开发 | user/base/lib/libutee |
| RCTEE时间服务层 | 自主开发 | user/base/lib/libc-rctee |
| 系统调用层 | 自主开发 | kernel/rctee/lib/rctee/rctee_core |
| 内核时间管理 | 复用（LK内核） | kernel/lk/kernel |
| ARM Generic Timer | 复用（LK驱动） | kernel/lk/dev/timer/arm_generic |
| SysTick Timer | 复用（LK驱动） | kernel/lk/arch/arm/arm-m/systick |
| 可信存储服务 | 复用（现有服务） | kernel/rctee/lib/storage |
| TA属性服务 | 复用（现有服务） | user/base/lib/libutee |

### 3.4 业务流程

#### 3.4.1 系统时间获取流程
**流程描述**：TA调用TEE_GetSystemTime获取当前系统时间
1. TA调用TEE_GetSystemTime API
2. 验证参数有效性
3. 根据保护级别选择时间源
4. 读取硬件定时器值
5. 转换为TEE_Time格式
6. 返回时间值给TA

#### 3.4.2 TA持久时间设置流程
**流程描述**：TA设置自己的持久时间基准点
1. TA调用TEE_SetTAPersistentTime
2. 验证参数和权限
3. 原子性写入可信存储
4. 更新TA时间状态为SUCCESS
5. 返回操作结果

#### 3.4.3 TA持久时间查询流程
**流程描述**：TA查询自己的持久时间
1. TA调用TEE_GetTAPersistentTime
2. 检查TA时间状态
3. 如果未设置，返回TIME_NOT_SET
4. 如果需要重置，返回TIME_NEEDS_RESET
5. 计算当前持久时间
6. 检查溢出情况
7. 返回时间值和状态

### 3.5 接口设计

#### 3.5.1 用户接口
基于代码库中的实际实现，时间API接口设计如下：

```c
// 时间结构体定义（GlobalPlatform标准）
typedef struct {
    uint32_t seconds;  // 自1970年1月1日UTC以来的秒数
    uint32_t millis;   // 毫秒数 (0-999)
} TEE_Time;

// TEE错误码定义（来自tee_api_defines.h）
#define TEE_SUCCESS                    0x00000000
#define TEE_ERROR_TIME_NOT_SET         0xFFFF5000
#define TEE_ERROR_TIME_NEEDS_RESET     0xFFFF5001
#define TEE_ERROR_OVERFLOW             0xFFFF300F
#define TEE_ERROR_OUT_OF_MEMORY        0xFFFF000C
#define TEE_ERROR_STORAGE_NO_SPACE     0xFFFF3041
#define TEE_ERROR_CANCEL               0xFFFF0002

// 超时常量
#define TEE_TIMEOUT_INFINITE           0xFFFFFFFF

// GlobalPlatform API函数声明
void TEE_GetSystemTime(TEE_Time* time);
TEE_Result TEE_Wait(uint32_t timeout);
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time);
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time);
void TEE_GetREETime(TEE_Time* time);

// 底层RCTEE接口（libc-rctee）
int rctee_gettime(clockid_t clock_id, int64_t* time);
int rctee_nanosleep(clockid_t clock_id, uint32_t flags, uint64_t sleep_time);
```

#### 3.5.2 外部接口
基于代码库分析的实际接口：

```c
// 系统调用接口（rctee_core/syscall.c）
long sys_gettime(uint32_t clock_id, uint32_t flags, user_addr_t time);
long sys_nanosleep(uint32_t clock_id, uint32_t flags, uint64_t sleep_time);
long sys_wait(uint32_t handle_id, user_addr_t user_event, uint32_t timeout_msecs);

// 硬件定时器接口（arm_generic_timer.c）
lk_time_ns_t current_time_ns(void);
lk_time_t current_time(void);
status_t platform_set_oneshot_timer(platform_timer_callback callback, lk_time_ns_t time_ns);

// 可信存储接口
int storage_open_session(storage_session_t* session_p, const char* port);
int storage_write(file_handle_t handle, uint64_t off, const void* buf, size_t size, uint32_t flags);
int storage_read(file_handle_t handle, uint64_t off, void* buf, size_t size);
```

#### 3.5.3 内部接口
```c
// 时间转换接口
static inline uint64_t lk_time_ns_to_cntpct(lk_time_ns_t time_ns);
static inline lk_time_ns_t cntpct_to_lk_time_ns(uint64_t cntpct);

// 线程等待接口（thread.c）
int thread_sleep_ns(lk_time_ns_t ns);
int wait_queue_block(wait_queue_t* wait, lk_time_t timeout);

// 属性管理接口（tee_api_property.c）
TEE_Result TEE_GetPropertyAsU32(TEE_PropSetHandle propsetOrEnumerator,
                               const char* name, uint32_t* value);
```

### 3.6 数据结构设计

#### 3.6.1 逻辑结构设计要点
基于代码库中的实际数据结构：

```c
// TEE时间结构体（GlobalPlatform标准）
typedef struct {
    uint32_t seconds;  // 秒数
    uint32_t millis;   // 毫秒数
} TEE_Time;

// TA持久时间状态（基于GP标准错误码）
typedef enum {
    TA_TIME_NOT_SET = TEE_ERROR_TIME_NOT_SET,      // 0xFFFF5000
    TA_TIME_SUCCESS = TEE_SUCCESS,                  // 0x00000000
    TA_TIME_NEEDS_RESET = TEE_ERROR_TIME_NEEDS_RESET // 0xFFFF5001
} ta_time_state_t;

// TA时间管理上下文
typedef struct {
    TEE_Time base_time;        // 基准时间点
    ta_time_state_t state;     // 当前状态
    uint32_t protection_level; // 保护级别（100或1000）
    uint64_t rtc_base;         // RTC基准值
    bool is_initialized;       // 初始化标志
} ta_time_context_t;

// 时间属性结构（基于property系统）
struct time_property {
    char name[64];             // 属性名称
    enum user_ta_prop_type type; // 属性类型
    union {
        uint32_t u32_value;    // 保护级别等
        bool bool_value;       // 布尔属性
        char str_value[256];   // 字符串属性
    };
};

// 可信存储中的TA时间数据布局
typedef struct {
    uint32_t magic;            // 魔数标识
    uint32_t version;          // 版本号
    TEE_Time persistent_time;  // 持久时间
    uint64_t rtc_reference;    // RTC参考值
    uint32_t checksum;         // 校验和
} ta_persistent_time_storage_t;
```

#### 3.6.2 物理结构设计要点
基于代码库中的存储实现：

- **可信存储布局**：
  - 文件路径：`/ta_time/{ta_uuid}.dat`
  - 数据对齐：32位边界对齐
  - 存储格式：二进制格式，包含魔数和校验和

- **内存管理**：
  - 时间结构体使用栈分配
  - 临时缓冲区通过malloc分配
  - 支持内存不足时的错误处理

- **原子性保证**：
  - 使用`STORAGE_OP_COMPLETE`标志确保原子写入
  - 写入前验证数据完整性
  - 失败时自动回滚机制

- **数据完整性**：
  - CRC32校验和验证
  - 魔数验证防止数据损坏
  - 版本号支持向后兼容

### 3.7 基于现有代码库的实现方案

#### 3.7.1 系统时间实现方案
基于`kernel/rctee/lib/rctee/rctee_core/syscall.c`中的`sys_gettime`实现：

```c
// 实现TEE_GetSystemTime
void TEE_GetSystemTime(TEE_Time* time) {
    int64_t ns_time;
    int rc = rctee_gettime(CLOCK_MONOTONIC, &ns_time);
    if (rc == 0) {
        time->seconds = (uint32_t)(ns_time / 1000000000ULL);
        time->millis = (uint32_t)((ns_time % 1000000000ULL) / 1000000ULL);
    }
}
```

保护级别通过属性系统查询：
- 使用`TEE_GetPropertyAsU32`查询`gpd.tee.systemTime.protectionLevel`
- 支持级别100（基于REE定时器）和1000（基于安全定时器）

#### 3.7.2 时间等待实现方案
基于`user/base/lib/libc-rctee/time.c`中的`rctee_nanosleep`：

```c
// 实现TEE_Wait
TEE_Result TEE_Wait(uint32_t timeout) {
    if (timeout == TEE_TIMEOUT_INFINITE) {
        // 无限等待的特殊处理
        return TEE_ERROR_NOT_SUPPORTED;
    }

    uint64_t sleep_ns = (uint64_t)timeout * 1000000ULL; // 毫秒转纳秒
    int rc = rctee_nanosleep(CLOCK_MONOTONIC, 0, sleep_ns);

    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}
```

支持可取消机制，通过线程取消标志实现。

#### 3.7.3 TA持久时间实现方案
利用现有的可信存储服务：

```c
// TA持久时间存储文件命名规则
#define TA_TIME_STORAGE_PREFIX "ta_time_"
#define TA_TIME_MAGIC 0x54415449  // "TATI"

// 实现TEE_SetTAPersistentTime
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time) {
    storage_session_t session;
    file_handle_t file;
    ta_persistent_time_storage_t storage_data;

    // 构造存储数据
    storage_data.magic = TA_TIME_MAGIC;
    storage_data.version = 1;
    storage_data.persistent_time = *time;
    storage_data.rtc_reference = current_time_ns();
    storage_data.checksum = calculate_crc32(&storage_data,
                                           sizeof(storage_data) - sizeof(uint32_t));

    // 原子性写入
    int rc = storage_write(file, 0, &storage_data, sizeof(storage_data),
                          STORAGE_OP_COMPLETE);

    return (rc >= 0) ? TEE_SUCCESS : TEE_ERROR_STORAGE_NO_SPACE;
}
```

#### 3.7.4 REE时间实现方案
通过系统调用获取REE时间：

```c
// 实现TEE_GetREETime
void TEE_GetREETime(TEE_Time* time) {
    int64_t ns_time;
    // 使用CLOCK_REALTIME获取REE时间
    int rc = rctee_gettime(CLOCK_REALTIME, &ns_time);
    if (rc == 0) {
        time->seconds = (uint32_t)(ns_time / 1000000000ULL);
        time->millis = (uint32_t)((ns_time % 1000000000ULL) / 1000000ULL);
    }
}
```

#### 3.7.5 时间保护级别实现
基于硬件能力和配置：

```c
// 查询系统时间保护级别
uint32_t get_system_time_protection_level(void) {
#if USE_IMX_MONOTONIC_TIME
    return 1000;  // 使用安全硬件定时器
#else
    return 100;   // 基于REE定时器
#endif
}

// 通过属性系统暴露
static const uint32_t system_time_protection_level = get_system_time_protection_level();
```

### 3.8 时间属性查询系统调用方案

#### 3.8.1 设计原则
采用直接系统调用方案，绕过现有的TIPC通信和服务层，提供高效的时间属性查询：

**简化的调用链：**
```
TA应用层 (TEE_GetProperty*)
    ↓
libutee属性API包装
    ↓
libc-rctee系统调用包装
    ↓
系统调用 (sys_get_time_property)
    ↓
内核直接处理
```

#### 3.8.2 系统调用设计

**系统调用表定义**

在`kernel/rctee/lib/rctee/include/syscall_table.h`中增加：

```c
// 时间属性查询系统调用
DEF_SYSCALL(0x50, get_time_property_u32, long, 2, user_addr_t name, user_addr_t value)
DEF_SYSCALL(0x51, get_time_property_string, long, 3, user_addr_t name, user_addr_t buffer, user_addr_t len)
DEF_SYSCALL(0x52, get_time_property_bool, long, 2, user_addr_t name, user_addr_t value)
```

**系统调用实现**

在`kernel/rctee/lib/rctee/rctee_core/syscall.c`中实现：

```c
// U32属性查询系统调用
long sys_get_time_property_u32(user_addr_t name_addr, user_addr_t value_addr) {
    char name_buffer[64];
    uint32_t value = 0;
    int rc;

    // 参数验证
    if (!name_addr || !value_addr) {
        return ERR_INVALID_ARGS;
    }

    // 从用户空间复制属性名称
    rc = copy_from_user(name_buffer, name_addr, sizeof(name_buffer) - 1);
    if (rc < 0) return rc;
    name_buffer[sizeof(name_buffer) - 1] = '\0';

    // 查询时间相关属性
    if (!strcmp(name_buffer, "gpd.tee.systemTime.protectionLevel")) {
#if USE_IMX_MONOTONIC_TIME
        value = 1000;  // 安全硬件定时器
#else
        value = 100;   // REE定时器
#endif
    } else if (!strcmp(name_buffer, "gpd.tee.TAPersistentTime.protectionLevel")) {
        // 根据存储类型确定保护级别
        value = 1000;  // 基于可信存储
    } else if (!strcmp(name_buffer, "gpd.tee.systemTime.clockFrequency")) {
        value = get_system_clock_frequency();
    } else if (!strcmp(name_buffer, "gpd.tee.systemTime.resolution")) {
        value = 1000000;  // 纳秒分辨率
    } else {
        return ERR_NOT_FOUND;
    }

    // 复制结果到用户空间
    return copy_to_user(value_addr, &value, sizeof(value));
}

// 字符串属性查询系统调用
long sys_get_time_property_string(user_addr_t name_addr,
                                 user_addr_t buffer_addr,
                                 user_addr_t len_addr) {
    char name_buffer[64];
    char value_buffer[256];
    uint32_t buffer_len;
    int rc;

    // 参数验证
    if (!name_addr || !buffer_addr || !len_addr) {
        return ERR_INVALID_ARGS;
    }

    // 获取缓冲区长度
    rc = copy_from_user(&buffer_len, len_addr, sizeof(buffer_len));
    if (rc < 0) return rc;

    // 复制属性名称
    rc = copy_from_user(name_buffer, name_addr, sizeof(name_buffer) - 1);
    if (rc < 0) return rc;
    name_buffer[sizeof(name_buffer) - 1] = '\0';

    // 查询字符串属性
    if (!strcmp(name_buffer, "gpd.tee.systemTime.clockSource")) {
#if USE_IMX_MONOTONIC_TIME
        strncpy(value_buffer, "secure_monotonic", sizeof(value_buffer) - 1);
#else
        strncpy(value_buffer, "arm_generic_timer", sizeof(value_buffer) - 1);
#endif
    } else if (!strcmp(name_buffer, "gpd.tee.implementation.version")) {
        strncpy(value_buffer, "RCTEE-1.0", sizeof(value_buffer) - 1);
    } else if (!strcmp(name_buffer, "gpd.tee.implementation.name")) {
        strncpy(value_buffer, "RCTEE", sizeof(value_buffer) - 1);
    } else if (!strcmp(name_buffer, "gpd.tee.TAPersistentTime.storageType")) {
        strncpy(value_buffer, "secure_storage", sizeof(value_buffer) - 1);
    } else {
        return ERR_NOT_FOUND;
    }

    value_buffer[sizeof(value_buffer) - 1] = '\0';
    uint32_t actual_len = strlen(value_buffer) + 1;

    // 检查缓冲区大小
    if (buffer_len < actual_len) {
        copy_to_user(len_addr, &actual_len, sizeof(actual_len));
        return ERR_NOT_ENOUGH_BUFFER;
    }

    // 复制结果
    rc = copy_to_user(buffer_addr, value_buffer, actual_len);
    if (rc < 0) return rc;

    return copy_to_user(len_addr, &actual_len, sizeof(actual_len));
}

// 布尔属性查询系统调用
long sys_get_time_property_bool(user_addr_t name_addr, user_addr_t value_addr) {
    char name_buffer[64];
    bool value = false;
    int rc;

    // 参数验证
    if (!name_addr || !value_addr) {
        return ERR_INVALID_ARGS;
    }

    // 从用户空间复制属性名称
    rc = copy_from_user(name_buffer, name_addr, sizeof(name_buffer) - 1);
    if (rc < 0) return rc;
    name_buffer[sizeof(name_buffer) - 1] = '\0';

    // 查询布尔属性
    if (!strcmp(name_buffer, "gpd.tee.systemTime.monotonic")) {
        value = true;  // 系统时间保证单调性
    } else if (!strcmp(name_buffer, "gpd.tee.TAPersistentTime.supported")) {
        value = true;  // 支持TA持久时间
    } else if (!strcmp(name_buffer, "gpd.tee.systemTime.rollbackDetection")) {
        value = true;  // 支持回滚检测
    } else {
        return ERR_NOT_FOUND;
    }

    // 复制结果到用户空间
    return copy_to_user(value_addr, &value, sizeof(value));
}
```

#### 3.8.3 libc-rctee层包装函数

**简化的包装函数设计**

在`user/base/lib/libc-rctee/rctee_property.h`中定义：

```c
// 时间属性查询接口
int rctee_get_time_property_u32(const char* name, uint32_t* value);
int rctee_get_time_property_string(const char* name, char* buffer, uint32_t* len);
int rctee_get_time_property_bool(const char* name, bool* value);
```

在`user/base/lib/libc-rctee/rctee_property.c`中实现：

```c
#include <rctee_syscalls.h>

int rctee_get_time_property_u32(const char* name, uint32_t* value) {
    if (!name || !value) {
        return ERR_INVALID_ARGS;
    }
    return _rctee_get_time_property_u32((user_addr_t)name, (user_addr_t)value);
}

int rctee_get_time_property_string(const char* name, char* buffer, uint32_t* len) {
    if (!name || !buffer || !len) {
        return ERR_INVALID_ARGS;
    }
    return _rctee_get_time_property_string((user_addr_t)name,
                                          (user_addr_t)buffer,
                                          (user_addr_t)len);
}

int rctee_get_time_property_bool(const char* name, bool* value) {
    if (!name || !value) {
        return ERR_INVALID_ARGS;
    }
    return _rctee_get_time_property_bool((user_addr_t)name, (user_addr_t)value);
}
```

#### 3.8.4 GlobalPlatform API层直接集成

**完全替换现有属性机制**

在`user/base/lib/libutee/tee_time_property.c`中新建专用文件：

```c
#include <rctee_property.h>
#include <tee_api_defines.h>

// 直接使用系统调用的TEE属性API
TEE_Result TEE_GetTimePropertyAsU32(const char* name, uint32_t* value) {
    int rc = rctee_get_time_property_u32(name, value);

    switch (rc) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_NOT_ENOUGH_BUFFER:
            return TEE_ERROR_SHORT_BUFFER;
        default:
            return TEE_ERROR_GENERIC;
    }
}

TEE_Result TEE_GetTimePropertyAsString(const char* name, char* buffer, uint32_t* len) {
    int rc = rctee_get_time_property_string(name, buffer, len);

    switch (rc) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_NOT_ENOUGH_BUFFER:
            return TEE_ERROR_SHORT_BUFFER;
        default:
            return TEE_ERROR_GENERIC;
    }
}

TEE_Result TEE_GetTimePropertyAsBool(const char* name, bool* value) {
    int rc = rctee_get_time_property_bool(name, value);

    switch (rc) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        default:
            return TEE_ERROR_GENERIC;
    }
}
```

#### 3.8.5 支持的时间相关属性列表

**U32类型属性：**
- `gpd.tee.systemTime.protectionLevel`: 系统时间保护级别 (100/1000)
- `gpd.tee.TAPersistentTime.protectionLevel`: TA持久时间保护级别 (1000)
- `gpd.tee.systemTime.clockFrequency`: 时钟频率 (Hz)
- `gpd.tee.systemTime.resolution`: 时间分辨率 (纳秒)

**字符串类型属性：**
- `gpd.tee.systemTime.clockSource`: 时钟源类型 ("secure_monotonic"/"arm_generic_timer")
- `gpd.tee.implementation.version`: TEE实现版本 ("RCTEE-1.0")
- `gpd.tee.implementation.name`: TEE实现名称 ("RCTEE")
- `gpd.tee.TAPersistentTime.storageType`: 存储类型 ("secure_storage")

**布尔类型属性：**
- `gpd.tee.systemTime.monotonic`: 系统时间单调性保证 (true)
- `gpd.tee.TAPersistentTime.supported`: TA持久时间支持 (true)
- `gpd.tee.systemTime.rollbackDetection`: 时间回滚检测支持 (true)

#### 3.8.6 使用示例

```c
// TA代码中查询时间属性
void query_time_properties(void) {
    uint32_t protection_level;
    char clock_source[64];
    uint32_t buffer_len = sizeof(clock_source);
    bool monotonic;

    // 查询系统时间保护级别
    TEE_Result res = TEE_GetTimePropertyAsU32("gpd.tee.systemTime.protectionLevel",
                                             &protection_level);
    if (res == TEE_SUCCESS) {
        TLOGI("System time protection level: %u\n", protection_level);
    }

    // 查询时钟源类型
    res = TEE_GetTimePropertyAsString("gpd.tee.systemTime.clockSource",
                                     clock_source, &buffer_len);
    if (res == TEE_SUCCESS) {
        TLOGI("Clock source: %s\n", clock_source);
    }

    // 查询单调性保证
    res = TEE_GetTimePropertyAsBool("gpd.tee.systemTime.monotonic", &monotonic);
    if (res == TEE_SUCCESS) {
        TLOGI("Monotonic guarantee: %s\n", monotonic ? "Yes" : "No");
    }
}
```

#### 3.8.7 性能和安全优势

**性能优势：**
- **直接系统调用**：绕过TIPC通信，减少上下文切换
- **无服务依赖**：不依赖Generic TA Service，启动更快
- **内核直接处理**：属性值在内核中直接计算，无需IPC
- **缓存友好**：常用属性可在内核中缓存

**安全优势：**
- **减少攻击面**：不经过用户态服务，减少潜在攻击点
- **参数验证**：系统调用层严格验证所有参数
- **权限控制**：可在系统调用层实现细粒度权限控制
- **内存安全**：使用copy_from_user/copy_to_user确保内存安全

这个纯系统调用方案提供了最高效、最安全的时间属性查询机制，完全满足GP标准要求。

## 4 系统出错处理设计

### 4.1 出错信息
| 错误类型 | 返回码 | 处理方法 |
|---------|--------|----------|
| 时间未设置 | TEE_ERROR_TIME_NOT_SET | 提示TA调用设置函数 |
| 时间需要重置 | TEE_ERROR_TIME_NEEDS_RESET | 建议重新同步时间 |
| 时间溢出 | TEE_ERROR_OVERFLOW | 截断秒数，返回溢出标志 |
| 内存不足 | TEE_ERROR_OUT_OF_MEMORY | 释放资源后重试 |
| 存储空间不足 | TEE_ERROR_STORAGE_NO_SPACE | 清理存储空间 |

### 4.2 补救措施
- **后备技术**：当安全定时器不可用时，降级使用REE定时器
- **降效技术**：保护级别从1000降级到100
- **恢复技术**：检测到时间异常时自动触发重置流程

## 5 系统维护设计
- 提供时间状态查询接口用于系统诊断
- 实现时间源切换的热插拔支持
- 设计时间精度监控和告警机制

## 6 尚未解决的问题
1. 多核环境下时间同步机制的具体实现
2. 时间回滚检测算法的优化
3. 大规模TA部署时的存储空间管理策略